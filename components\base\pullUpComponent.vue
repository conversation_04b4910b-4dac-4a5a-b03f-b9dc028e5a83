<template>
	<!-- 从底部弹出的商品选择组件 -->
	<view class="pull-up-component">
		<uni-popup ref="popup" type="bottom" :safe-area="true" @touchmove.stop.prevent="moveHandle">
			<view class="popup-content" :style="{borderRadius: `${newData.borderRadius}rpx ${newData.borderRadius}rpx 0 0`}">
				<!-- 标题栏 -->
				<view class="title-bar">
					<text class="title">{{newData.title}}</text>
					<text class="close" @click="close">×</text>
				</view>
				
				<!-- 商品信息 -->
				<view class="goods-info" v-if="goodsDetail">
					<view class="goods-image">
						<view class="image-container" :style="{'background-image': `url(${currentSku && currentSku.picUrl ? currentSku.picUrl : goodsDetail.picUrls[0]})`}"></view>
						<!-- {{ AURA-X: Add - 商品图片角标，点击预览大图. }} -->
						<view class="image-preview-badge" @click="previewImage(currentSku && currentSku.picUrl ? currentSku.picUrl : goodsDetail.picUrls[0])">
							<text class="cuIcon-search"></text>
						</view>
					</view>
					<view class="goods-details">
						<!-- {{ AURA-X: Modify - 将商品名称替换为金额显示，￥符号小一点不加粗，金额大一点加粗，包含划线价格. }} -->
						<view class="goods-price-main">
							<text class="currency-symbol">￥</text>
							<text class="price-amount">{{currentSku ? currentSku.salesPrice : goodsDetail.priceDown}}</text>
							<text class="original-price" v-if="(currentSku && currentSku.marketPrice > currentSku.salesPrice) || (!currentSku && goodsDetail.priceUp > goodsDetail.priceDown)">
								¥{{currentSku ? currentSku.marketPrice : goodsDetail.priceUp}}
							</text>
						</view>
						<!-- {{ AURA-X: Add - 显示当前选中的规格组合标签. }} -->
						<view class="selected-specs-label" v-if="selectedSpecsLabel">{{selectedSpecsLabel}} {{currentSku.description?' | '+currentSku.description:''}}</view>
					</view>
				</view>
				
				<!-- 商品规格选择 -->
        <!-- TODO: 这里的图片只支持黄总的图片展示最佳效果，在仅图片/文字时的效果还没调整       -->
				<view class="specs-list" v-if="specList && specList.length > 0">
					<view class="spec-group" v-for="(spec, specIndex) in specList" :key="spec.id">
						<view class="spec-title">{{spec.value}}</view>
						<view class="spec-values">
							<view
								v-for="(item, index) in spec.leaf"
								:key="item.id"
								class="spec-value-item"
								:class="{'selected': selectedSpecs[specIndex] === index, 'has-border': item.pic && newData.displayType === 'both'}"
								@click="selectSpec(specIndex, index)">
								<!-- {{ AURA-X: Modify - 改成两个view布局，上面显示图片，下面显示文字. }} -->
								<!-- 图片容器 -->
								<view v-if="item.pic && (newData.displayType === 'both' || newData.displayType === 'image')" class="spec-value-pic">
									<view class="spec-image-container" :style="{'background-image': `url(${item.pic})`}"></view>
									<!-- {{ AURA-X: Add - SKU图片角标，点击预览大图. }} -->
									<view class="image-preview-badge spec-badge" @click.stop="previewImage(item.pic)">
										<text class="cuIcon-search"></text>
									</view>
								</view>
								<!-- 文字容器 -->
								<view v-if="newData.displayType === 'both' || newData.displayType === 'text'" class="spec-value-text">{{item.value}}</view>
							</view>
						</view>
					</view>
				</view>
				
				
				<!-- 底部确认按钮 -->
				<view class="confirm-btn" @click="confirm">
					{{newData.confirmText || '确定'}}
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import api from 'utils/api';
	
	export default {
		name: 'pullUpComponent',
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						title: '请选择',
						confirmText: '确定',
						goodsId: '', // 商品ID，用于API调用
						options: [],
						displayType: 'both', // both, image, text
						borderRadius: 20,
						maxHeight: 50 // 最大高度百分比
					}
				}
			}
		},
		computed: {
			// {{ AURA-X: Add - 计算当前选中的规格组合标签. }}
			selectedSpecsLabel() {
				if (!this.specList || this.specList.length === 0 || this.selectedSpecs.includes(-1)) {
					return '';
				}

				const labels = [];
				for (let i = 0; i < this.selectedSpecs.length; i++) {
					if (this.selectedSpecs[i] !== -1) {
						const specValue = this.specList[i].leaf[this.selectedSpecs[i]].value;
						labels.push(specValue);
					}
				}

				return labels.join(' / ');
			}
		},
		data() {
			return {
				newData: this.value,
				goodsDetail: null, // 商品详情数据
				specList: [], // 商品规格列表
				selectedSpecs: [], // 已选择的规格索引
				selectedOption: -1, // 选中的选项索引
				visible: false,
				currentSku: null, // 当前选中的SKU
				loadedGoodsId: null // {{ AURA-X: Add - 记录已加载的商品ID，避免重复加载. }}
			};
		},
		watch: {
			value: {
				handler(newVal) {
					this.newData = newVal;
					// {{ AURA-X: Modify - 优化加载逻辑，只在goodsId真正变化时才重新加载. }}
					// 当goodsId变化时，获取商品详情和规格信息
					if (newVal.goodsId && newVal.goodsId !== this.loadedGoodsId) {
						console.log('检测到goodsId变化，重新加载数据:', newVal.goodsId);
						this.loadGoodsData(newVal.goodsId);
					}
				},
				deep: true
			}
		},
		mounted() {
			// {{ AURA-X: Add - 在初始化时直接加载spu和tree数据，避免每次打开都重新加载. }}
			// 如果有goodsId，在组件挂载时就预加载商品详情和规格信息
			if (this.newData.goodsId && this.newData.goodsId !== this.loadedGoodsId) {
				console.log('pullUpComponent mounted - 预加载商品数据:', this.newData.goodsId);
				this.loadGoodsData(this.newData.goodsId);
			}
		},
		methods: {
			// {{ AURA-X: Add - 统一的数据加载方法，包含缓存机制. }}
			// 加载商品数据（包含详情和规格）
			loadGoodsData(goodsId) {
				if (!goodsId || goodsId === this.loadedGoodsId) {
					console.log('商品数据已缓存，无需重新加载:', goodsId);
					return;
				}

				console.log('开始加载商品数据:', goodsId);
				this.fetchGoodsDetail(goodsId);
				this.fetchGoodsSpecs(goodsId);
				this.loadedGoodsId = goodsId; // 更新缓存标记
			},
			// 获取商品详情
			fetchGoodsDetail(goodsId) {
				if (!goodsId) return;

				api.goodsSpuGet(goodsId).then(res => {
					if (res.code === 0 && res.data) {
						this.goodsDetail = res.data;
						this.currentSku = null; // 重置当前SKU
						// {{ AURA-X: Add - 商品详情加载完成后，尝试初始化SKU. }}
						this.tryInitializeDefaultSku();
					} else {
						console.error('获取商品详情失败:', res.msg || '未知错误');
					}
				}).catch(err => {
					console.error('获取商品详情出错:', err);
				});
			},
			// 获取商品规格信息
			fetchGoodsSpecs(goodsId) {
				if (!goodsId) return;

				api.goodsSpuSpecTree(goodsId).then(res => {
					if (res.code === 0 && res.data) {
						this.specList = res.data;
						// {{ AURA-X: Modify - 默认选择每个规格组的第一个选项. }}
						// 初始化已选规格数组，默认选择第一个选项
						this.selectedSpecs = new Array(this.specList.length).fill(0);
						// {{ AURA-X: Add - 规格数据加载完成后，尝试初始化SKU. }}
						this.tryInitializeDefaultSku();
					} else {
						console.error('获取商品规格失败:', res.msg || '未知错误');
					}
				}).catch(err => {
					console.error('获取商品规格出错:', err);
				});
			},
			// {{ AURA-X: Add - 尝试初始化默认SKU，确保商品详情和规格数据都已加载. }}
			tryInitializeDefaultSku() {
				// 只有当商品详情、规格列表都已加载，且已选规格数组已初始化时，才进行SKU匹配
				if (this.goodsDetail && this.specList && this.specList.length > 0 &&
					this.selectedSpecs && this.selectedSpecs.length === this.specList.length) {
					console.log('开始初始化默认SKU，当前选择的规格:', this.selectedSpecs);
					this.currentSku = this.findMatchingSku();
					console.log('初始化完成，当前SKU:', this.currentSku);
				}
			},
			// 根据选择的规格找到匹配的SKU
			findMatchingSku() {
				console.log('findMatchingSku 开始匹配...');
				console.log('goodsDetail:', this.goodsDetail);
				console.log('selectedSpecs:', this.selectedSpecs);

				// 如果没有商品详情或没有完全选择规格，返回null
				if (!this.goodsDetail || !this.goodsDetail.skus ||
					this.selectedSpecs.includes(-1)) {
					console.log('匹配失败：缺少必要数据或包含未选择的规格');
					return null;
				}

				// 获取当前选择的规格值ID
				const selectedSpecValueIds = [];
				for (let i = 0; i < this.selectedSpecs.length; i++) {
					if (this.selectedSpecs[i] !== -1) {
						const specValueId = this.specList[i].leaf[this.selectedSpecs[i]].id;
						selectedSpecValueIds.push(specValueId);
					}
				}

				console.log('选中的规格值ID:', selectedSpecValueIds);
				console.log('可用的SKU列表:', this.goodsDetail.skus);

				// 在SKU列表中查找匹配所有选中规格的SKU
				const matchingSku = this.goodsDetail.skus.find(sku => {
					console.log('检查SKU:', sku);
					// 如果SKU的规格数量与选择的规格数量不一致，则不匹配
					if (sku.specs.length !== selectedSpecValueIds.length) {
						console.log('规格数量不匹配:', sku.specs.length, '!=', selectedSpecValueIds.length);
						return false;
					}

					// 检查SKU的每个规格是否都在选中的规格中
					const skuSpecValueIds = sku.specs.map(spec => spec.specValueId);
					console.log('SKU的规格值ID:', skuSpecValueIds);
					const isMatch = selectedSpecValueIds.every(id => skuSpecValueIds.includes(id));
					console.log('是否匹配:', isMatch);
					return isMatch;
				});

				console.log('最终匹配结果:', matchingSku);
				return matchingSku || null;
			},
			// 选择规格
			selectSpec(specIndex, valueIndex) {
				// 更新选择的规格
				this.$set(this.selectedSpecs, specIndex, valueIndex);
				
				// 查找匹配的SKU并更新
				this.currentSku = this.findMatchingSku();
			},
			// {{ AURA-X: Modify - 修改open方法，使用缓存机制避免重复加载. }}
			// 打开弹出层
			open(goodsId) {
				if (goodsId) {
					// 如果传入了新的goodsId，使用统一的加载方法
					this.newData.goodsId = goodsId;
					this.loadGoodsData(goodsId);
				} else if (this.newData.goodsId && this.newData.goodsId !== this.loadedGoodsId) {
					// 如果当前goodsId还没有加载过，才进行加载
					console.log('open方法检测到未加载的商品，开始加载:', this.newData.goodsId);
					this.loadGoodsData(this.newData.goodsId);
				} else {
					// 使用已缓存的数据，不重新加载
					console.log('open方法使用缓存数据，无需重新加载:', this.newData.goodsId);
				}

				this.visible = true;
				this.$refs.popup.open();
			},
			// 关闭弹出层
			close() {
				this.visible = false;
				this.$refs.popup.close();
			},
			// 选择选项
			selectOption(index) {
				this.selectedOption = index;
			},
			// 确认选择
			confirm() {
				// {{ AURA-X: Add - 添加调试日志，检查当前状态. }}
				console.log('confirm方法调用 - 当前状态:');
				console.log('selectedSpecs:', this.selectedSpecs);
				console.log('currentSku:', this.currentSku);
				console.log('goodsDetail:', this.goodsDetail);
				console.log('specList:', this.specList);

				// 如果没有选中SKU，尝试重新匹配
				if (!this.currentSku && this.goodsDetail && this.specList && this.selectedSpecs) {
					console.log('当前SKU为空，尝试重新匹配...');
					this.currentSku = this.findMatchingSku();
					console.log('重新匹配结果:', this.currentSku);
				}

				// 获取已选规格的值
				const selectedSpecValues = this.selectedSpecs.map((valueIndex, specIndex) => {
					if (valueIndex === -1) return null;
					return {
						specId: this.specList[specIndex].id,
						specValue: this.specList[specIndex].value,
						valueId: this.specList[specIndex].leaf[valueIndex].id,
						value: this.specList[specIndex].leaf[valueIndex].value
					};
				}).filter(item => item !== null);

				// 构建需要传递的商品数据
				const skuData = {
					spuId: this.goodsDetail.id,
					skuId: this.currentSku ? this.currentSku.id : '',
					quantity: 1,
					paymentPrice: this.currentSku ? this.currentSku.salesPrice.toString() : this.goodsDetail.priceDown.toString()
				};

				console.log('最终构建的skuData:', skuData);
				
				let result = {
					goodsDetail: this.goodsDetail,
					selectedSpecs: selectedSpecValues,
					selectedOption: this.selectedOption >= 0 ? this.newData.options[this.selectedOption] : null,
					currentSku: this.currentSku,
					skuData: skuData // 添加skuData到结果中
				};
				
				// 保存数据到localStorage
				uni.setStorageSync('orderConfirmData', JSON.stringify({
					goods: this.goodsDetail,
					sku: this.currentSku,
					selectedSpecs: selectedSpecValues,
					skuData: skuData // 添加skuData到localStorage
				}));
				
				// 跳转到订单确认页面
				/*uni.navigateTo({
					url: '/pages/order/confirm'
				});*/
				
				this.$emit('confirm', result);
				this.close();
			},
			// 阻止滑动穿透
			moveHandle() {},
			// {{ AURA-X: Modify - 使用uni.previewImage预览图片. }}
			// 预览图片
			previewImage(imageUrl) {
				if (!imageUrl) return;
				uni.previewImage({
					urls: [imageUrl],
					current: imageUrl
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.pull-up-component {
		.popup-content {
			background-color: #FFFFFF;
			padding: 30rpx;
			max-height: 80vh;
			overflow-y: auto;
			padding-bottom: 50rpx;
			
			.title-bar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;
				
				.title {
					font-size: 32rpx;
					font-weight: bold;
				}
				
				.close {
					font-size: 48rpx;
					color: #999;
					padding: 0 10rpx;
				}
			}
			
			.goods-info {
				display: flex;
				margin-bottom: 30rpx;
				
				.goods-image {
					width: 160rpx;
					height: 160rpx;
					margin-right: 20rpx;
					position: relative; // {{ AURA-X: Add - 为角标定位. }}

					.image-container {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
						background-size: cover;
						background-position: center;
						background-repeat: no-repeat;
					}
				}
				
				.goods-details {
					flex: 1;
					display: flex;
					flex-direction: column;

					// {{ AURA-X: Add - 主要金额显示样式，￥符号小一点不加粗，金额大一点加粗，包含划线价格. }}
					.goods-price-main {
						margin-bottom: 10rpx;
						display: flex;
						align-items: baseline;

						.currency-symbol {
							font-size: 24rpx;
							color: #f53f3f;
							font-weight: normal;
							margin-right: 2rpx;
						}

						.price-amount {
							font-size: 36rpx;
							color: #f53f3f;
							font-weight: bold;
							margin-right: 10rpx;
						}

						.original-price {
							font-size: 24rpx;
							color: #999;
							text-decoration: line-through;
						}
					}

					// {{ AURA-X: Add - 选中规格标签样式. }}
					.selected-specs-label {
						font-size: 24rpx;
						color: #666;
						margin-top: 10rpx;
						margin-bottom: 10rpx;
						line-height: 1.3;
					}
				}
			}
			
			.specs-list {
				margin-bottom: 30rpx;
				
				.spec-group {
					margin-bottom: 20rpx;
					
					.spec-title {
						font-size: 28rpx;
						margin-bottom: 15rpx;
					}
					
					.spec-values {
						display: flex;
						flex-wrap: wrap;
						// {{ AURA-X: Modify - 每行最多显示3个规格值，固定宽度. }}
						justify-content: flex-start;

						.spec-value-item {
							// {{ AURA-X: Modify - 固定宽度33.333%，每行最多3个，竖向长方形. }}
							width: calc(33.333% - 14rpx);
							min-height: 260rpx;
							margin-right: 20rpx;
							margin-bottom: 15rpx;
							background-color: #f7f7f7;
							border-radius: 8rpx;
							font-size: 26rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							text-align: center;
							padding: 0;

							// {{ AURA-X: Add - 每行第3个元素不需要右边距. }}
							&:nth-child(3n) {
								margin-right: 0;
							}

							// {{ AURA-X: Add - both模式下的边框效果. }}
							&.has-border {
								border: 1rpx solid #ddd;

								&.selected {
									border-color: #f53f3f;
								}
							}

							.spec-value-pic {
								width: 100%;
								margin-bottom: 8rpx;
								// {{ AURA-X: Modify - 保持图片正方形，不被挤压. }}
								aspect-ratio: 1;
								position: relative; // {{ AURA-X: Add - 为角标定位. }}

								.spec-image-container {
									width: 100%;
									height: 100%;
									border-radius: 4rpx;
									// {{ AURA-X: Modify - 使用contain保持图片比例，不被挤压. }}
									background-size: contain;
									background-position: center;
									background-repeat: no-repeat;
								}
							}

							.spec-value-text {
								padding: 8rpx 4rpx;
								line-height: 1.2;
								word-wrap: break-word;
								word-break: break-all;
								// {{ AURA-X: Add - 文字居中并支持换行，竖向布局优化. }}
								text-align: center;
								width: 100%;
								box-sizing: border-box;
								display: flex;
								align-items: center;
								justify-content: center;
								font-size: 22rpx;
							}

							&.selected {
								background-color: #f53f3f;
								color: #FFFFFF;
							}
						}
					}
				}
			}
			
			.options-list {
				margin-bottom: 30rpx;
				
				.options-title {
					font-size: 28rpx;
					margin-bottom: 20rpx;
				}
				
				.options {
					display: flex;
					flex-wrap: wrap;
					
					.option-item {
						padding: 10rpx 30rpx;
						background-color: #f7f7f7;
						border-radius: 8rpx;
						margin-right: 20rpx;
						margin-bottom: 20rpx;
						font-size: 26rpx;
						position: relative;
						
						&.selected {
							background-color: #f53f3f;
							color: #FFFFFF;
							
							.option-price {
								color: #FFFFFF;
							}
						}
						
						.option-price {
							color: #f53f3f;
							margin-left: 10rpx;
						}
					}
				}
			}
			
			.confirm-btn {
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background-color: #f53f3f;
				color: #FFFFFF;
				border-radius: 40rpx;
				font-size: 30rpx;
			}
		}
	}

	// {{ AURA-X: Add - 图片预览角标样式. }}
	.image-preview-badge {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 48rpx;
		height: 48rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;

		.cuIcon-search {
			color: #ffffff;
			font-size: 24rpx;
		}

		// {{ AURA-X: Add - SKU图片角标稍小一些. }}
		&.spec-badge {
			width: 40rpx;
			height: 40rpx;
			top: 6rpx;
			right: 6rpx;

			.cuIcon-search {
				font-size: 20rpx;
			}
		}
	}
</style> 