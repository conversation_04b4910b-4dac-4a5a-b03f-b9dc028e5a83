{
    "name" : "user",
    "appid" : "__UNI__F08F30F",
    "description" : "",
    "versionName" : "1.0.13",
    "versionCode" : 107,
    "transformPx" : false,
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Payment" : {},
            "Push" : {},
            "Share" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            "ios" : {
                "privacyDescription" : {
                    "NSLocalNetworkUsageDescription" : "获取商品相关的数据内容"
                }
            },
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wxc85c48c1dd42655a",
                        "UniversalLinks" : "https://www.joolun.com/"
                    }
                },
                "push" : {},
                "oauth" : {},
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxc85c48c1dd42655a",
                        "UniversalLinks" : "https://www.joolun.com/"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx4c280d1cf1bab14a",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "es6" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于小程序位置接口的效果展示"
            }
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "NWJBZ-OOZWD-DEH4O-H2Z26-L4M4F-OZF4Z"
                }
            }
        },
        "devServer" : {
            "disableHostCheck" : true,
            "port" : 8086,
            //转发代理
            "proxy" : {
                "/weixinapi" : {
                    // "target" : "https://gongjunqi.com:9999", //测试环境
                    "target" : "http://drone.meta.taxer80.com", //测试环境

                    //                     "target" : "http://localhost:9999", //测试环境
                    //                    "target" : "https://show.gocreateone.com", //生产环境
                    "pathRewrite" : {
                        "^/weixinapi" : "/weixinapi"
                    }
                },
                "/mallapi" : {
                    // "target" : "https://gongjunqi.com:9999", //测试环境
                    "target" : "http://drone.meta.taxer80.com", //测试环境

                    //                    "target" : "http://localhost:9999", //测试环境
                    //                    "target" : "https://back.gocreateone.com:9999", //生产环境
                    "pathRewrite" : {
                        "^/mallapi" : "/mallapi"
                    }
                }
            },
            "https" : false
        },
        "router" : {
            "mode" : "history"
        },
        "title" : "",
        "domain" : "show.gocreateone.com"
    }
}
