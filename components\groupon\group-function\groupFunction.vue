<!-- 以这个为主 -->
<template>
	<!-- 多功能按钮组件 -->
	<view class="functionButton bg-white" :class="buttonPositionClass" :style="buttonStyleString">
		<!-- {{newData}} -->
		<view class="functionButtonComponent"
			:style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`}">
			<!--    {{ newData }}-->
			<view class="button_item">
				<view v-for="(item,index) in newData.buttonList" :key="index" :style="{width: `${item.width}%`}">
					<view v-if="item.showType==1" @click="buttonClick(item)" class="button_content" :style="{color: `${item.fontColor}`,
			fontSize: `${item.fontSize}px`,
			height: `${newData.height}px`,
		        letterSpacing: `${item.fontSpacing}px`,
		        fontWeight:`${item.fontWeight?'bold':'normal'}`,
		        backgroundColor:`${item.backColor}`}">{{ item.title }}
					</view>
					<view v-if="item.showType==2" class="bg-img" @click="buttonClick(item)"
						:style="{'background-image': 'url('+`${item.imgUrl}`+')',
						'height': `${newData.height}px`,
						'background-position': item.backgroundPosition || 'center',
						'background-repeat': item.backgroundRepeat || 'no-repeat',
						'background-size': item.backgroundSize || 'cover'
						}">
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="moveHandle">
			<uni-popup-dialog v-if="popupType == 1" mode="base" message="成功消息" :content="popupContent" :duration="2000"
				:before-close="true" @close="close" @confirm="confirm"></uni-popup-dialog>
			<view v-if="popupType == 2" style="width:550rpx" @click="close()">
				<image style="width: 100%;display: block;" mode="widthFix" :src="popupImg"></image>
			</view>
			<!-- 订单信息 -->
			<view v-if="popupType == 3" style="width:600rpx" @click="close()">
				<view class="cu-card">
					<view class="cu-item">
						<view v-if="!order || order.isPay == '0'">
							<view class="solid-bottom text-xxl padding text-center">
								<text class="cuIcon-roundcheckfill text-yellow"> 您还没有购买</text>
							</view>
						</view>
						<view v-if="order && order.isPay == '1'">
							<view class="solid-bottom text-xxl padding text-center">
								<text class="cuIcon-roundcheckfill text-green"> 支付成功</text>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单名称</text>
								<view class="flex-twice text-sm text-gray">{{order.name}}</view>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单编号</text>
								<view class="flex-twice text-sm text-gray">{{order.orderNo}}</view>
							</view>
							<view class="margin-left-sm margin-top flex" >
								<text class="margin-left flex-sub text-sm">付款金额</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentPrice?order.paymentPrice:'0.00'}}</view>
							</view>					
							<view class="margin-left-sm margin-top flex" v-if="order.paymentTime">
								<text class="margin-left flex-sub text-sm">付款时间</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentTime}}</view>
							</view>
							<view class="padding flex flex-direction">
								<!-- <button class="cu-btn margin-tb-sm lg goBackButton " @tap="toBack()">返回</button> -->
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
		
		<!-- <button @click="open">打开弹窗</button> -->
		<uni-popup ref="limituserpopup" type="dialog">
			<uni-popup-dialog title="请填写手机号码" mode="input"
				:duration="2000" :before-close="true" @close="closeLimituser" @confirm="confirmlLimituser">
			</uni-popup-dialog>
		</uni-popup>
		<!-- <button>手机号码限制框</button> -->
		<uni-popup ref="captainPhoneMessage" type="message">
			<uni-popup-message type="warn" message="请输入正确的手机号码" :duration="2000"></uni-popup-message>
		</uni-popup>
		
		<!-- 商品选择弹出层 -->
		<pull-up-component ref="pullUpComponent" v-model="pullUpData" @confirm="onPullUpConfirm"></pull-up-component>
	</view>

</template>

<script>
	const app = getApp();
	import pullUpComponent from '@/components/base/pullUpComponent.vue';
	
	export default {
		name: 'groupFunction', // 添加组件名称
		components: {
			pullUpComponent
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			order: {
			    type: Object,
			    default: function() {
			        return {}
			    }
			}
		},
		watch:{
			value: {
				handler(newVal) {
					this.newData = newVal;
					// 确保背景样式属性设置正确
					if (this.newData && this.newData.buttonList && this.newData.buttonList.length > 0) {
						this.newData.buttonList.forEach(item => {
							if (item.showType == 2) {
								// 设置默认背景样式属性
								item.backgroundPosition = item.backgroundPosition || 'left center';
								item.backgroundRepeat = item.backgroundRepeat || 'no-repeat';
								item.backgroundSize = item.backgroundSize || '100% 100%';
							}
						});
					}
					// 样式可能需要更新
					this.updateButtonPosition();
				},
				deep: true
			}
		},
		computed: {
			// 使用计算属性返回按钮位置的类名
			buttonPositionClass() {
				return this.newData.location == 2 ? 'button-fixed-bottom' : '';
			},
			// 使用计算属性返回动态样式字符串
			buttonStyleString() {
				if (this.newData.location == 2 && this.top && this.height) {
					return `top:${this.top - this.height}px;`;
				}
				return '';
			}
		},
		created() {
				this.width = uni.getSystemInfoSync().windowWidth;
				this.top = uni.getSystemInfoSync().windowHeight;	
		},
		mounted() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.functionButton').boundingClientRect(data => {
					 this.height = data.height;
					 // 获取高度后更新按钮位置
					 this.updateButtonPosition();
				}).exec();
				console.log("拼团按钮", this.order);

				// 确保每个按钮项都有背景样式属性
				if (this.newData && this.newData.buttonList && this.newData.buttonList.length > 0) {
					this.newData.buttonList.forEach(item => {
						if (item.showType == 2) {
							// 设置默认背景样式属性
							item.backgroundPosition = item.backgroundPosition || 'left center';
							item.backgroundRepeat = item.backgroundRepeat || 'no-repeat';
							item.backgroundSize = item.backgroundSize || '100% 100%';
						}
					});
				}

				// {{ AURA-X: Add - 在初始化时设置pullUpData，避免每次打开都重新设置. }}
				this.initPullUpData();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
				orderInfo: {}, //订单信息
				buttonOrder: {}, //按钮订单信息
				popupContent: '', //弹出框内容
				popupImg: '', //弹出框内容
				popupType: 0, //弹出框类型
				width: 0,
				top: 0,
				height: 0,
				pullUpData: {} // 底部弹出组件数据
			};
		},
		methods: {
			buttonClick(item) {
				console.log("点击按钮", item)
				if (item.button.type === 1) {
					this.jumpUrl(item.button);
				} else if (item.button.type === 2) {
					this.joinGroupon(item.button.type);
				} else if (item.button.type === 3) {
					this.getOrderInfo(item.button);
				} else if (item.button.type === 4) {
					this.showDialoag(item.button);
				} else if (item.button.type === 5) {
					this.phone(item.button);
				} else if (item.button.type === 6) {
					this.joinGroupon(item.button.type);
				} else if (item.button.type === 7) {
					this.showPullUp();
				}
			},
			//页面跳转
			jumpUrl(obj) {
				console.log("页面跳转", obj)
				if (!obj.pageUrl || obj.pageUrl.indexOf('/') < 0) {
					return;
				}
				if (obj.isSystemUrl) {
					uni.navigateTo({
						url: obj.pageUrl,
            // 失败回调
            fail: (res) => {
              console.log(res)
              if (res.errMsg==="navigateTo:fail can not navigateTo a tabbar page"){
                uni.switchTab({
                  url: obj.pageUrl,
                })
              }else{
                console.log(res)
              }
             /* uni.switchTab({
                url: obj.pageUrl,
              })*/
            }
					});
				} else {
					window.location.href = obj.pageUrl;
				}
			},
			//参加拼团
			joinGroupon(type) {
				if(type ==2){ //参加拼团
					 
				}else if(type ==6){ // 单独购买
					
				}
				this.$emit("joinGroupnAndPay",type)
			},
			close() {
				this.$refs.popup.close()
			},
			confirm() {
				this.$refs.popup.close()
			},
			//查看订单
			getOrderInfo() {
				this.popupType = 3;
				this.$refs.popup.open('center')
			},
			//弹窗显示
			showDialoag(obj) {
				console.log("弹窗显示", obj)
				this.popupType = obj.popupType;
				// 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
				this.popupContent = obj.content;
				this.popupImg = obj.imgUrl;
				this.$refs.popup.open('center')
			},
			//电话号码
			phone(obj) {
				console.log("电话号码", obj)
				uni.makePhoneCall({
					// 手机号
					phoneNumber: obj.phone,
					// 成功回调
					success: (res) => {},
					// 失败回调
					fail: (res) => {}
				});
			},
			paySuccess() {
				uni.redirectTo({
					url: '/pages/spellgroup/groupon-pay-result/index?id=' + this.orderInfo.id
				});
			},
			//更新按钮位置
			updateButtonPosition() {
				if (this.newData.location == 2 && this.height) { //固定悬浮底部
					//父页面高度  是否查询按钮订单
					this.$emit("bottomHeightShow", this.height);
				}
			},
			closeLimituser() {
				this.$refs.limituserpopup.close()
			},
			confirmlLimituser(value) {
				// 输入框的值
				if (!(/^1[3456789]\d{9}$/.test(value))) {
					console.log("格式不正确")
					this.$refs.captainPhoneMessage.open()
					return
				}
				this.buttonOrder.phone = value;
				this.joinGroupon(this.buttonOrder)
				this.$refs.limituserpopup.close()
			},
			// {{ AURA-X: Add - 初始化pullUpData，在组件挂载时执行一次. }}
			initPullUpData() {
				// 查找页面中的pullUpComponent组件数据
				let pullUpComponent = null;
				if (this.$parent && this.$parent.pageDivData &&
					this.$parent.pageDivData.pageComponent &&
					this.$parent.pageDivData.pageComponent.componentsList) {

					pullUpComponent = this.$parent.pageDivData.pageComponent.componentsList.find(
						item => item.componentName === 'pullUpComponent'
					);
				}

				if (pullUpComponent && pullUpComponent.data) {
					// 转换数据格式，将selectedGoods替换为goodsId
					const pullUpData = { ...pullUpComponent.data };
					if (pullUpData.selectedGoods && pullUpData.selectedGoods.id) {
						pullUpData.goodsId = pullUpData.selectedGoods.id;
						delete pullUpData.selectedGoods; // 移除selectedGoods属性
					}
					this.pullUpData = pullUpData;
					console.log('初始化pullUpData完成:', this.pullUpData);
				} else {
					console.error('未找到pullUpComponent组件数据');
				}
			},
			// {{ AURA-X: Modify - 简化showPullUp方法，不再重新设置数据，直接打开弹窗. }}
			// 显示底部弹出层
			showPullUp() {
				// 如果pullUpData还没有初始化，先初始化
				if (!this.pullUpData.goodsId) {
					console.log('pullUpData未初始化，先进行初始化');
					this.initPullUpData();
				}

				// 直接打开弹窗，不重新设置数据
				if (this.pullUpData.goodsId) {
					console.log('打开商品选择弹窗，使用缓存数据:', this.pullUpData.goodsId);
					this.$refs.pullUpComponent.open();
				} else {
					console.error('无法获取商品数据，无法打开弹窗');
				}
			},
			// 处理底部弹出层确认事件
			onPullUpConfirm(result) {
				console.log('商品选择结果:', result);
				// 从结果中提取skuData
				if (result && result.skuData) {
					// 调用父组件的joinGroupnAndPay方法，传入类型2（参团）和skuData
					this.$emit("joinGroupnAndPay", 2, result.skuData);
				} else {
					console.error('商品选择结果中缺少skuData');
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.functionButton {
		&.button-fixed-bottom {
			display: block;
			position: fixed;
			width: 100%;
			z-index: 2;
		}
	}

	.button_item {
		align-items: center;
		display: flex;
		text-align: center;
		//垂直方向元素居中，两边留白
	}

	.button_content {
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
