<!--
  - 用户协议页面
  - 暂时显示空白内容，后续可添加具体协议内容
-->
<template>
	<view class="agreement-container">
		<view class="agreement-content">
			<!-- 协议标题 -->
			<view class="agreement-header">
				<text class="agreement-title">用户服务协议</text>
				<text class="update-time">更新时间：2024年01月01日</text>
			</view>
			
			<!-- 协议内容 -->
			<view class="agreement-body">
				<view class="content-placeholder">
					<text class="placeholder-text">用户协议内容待完善...</text>
					<text class="placeholder-desc">
						此处将显示详细的用户服务协议条款，包括但不限于：
						用户权利与义务、服务内容、使用规范、免责声明等。
					</text>
				</view>
				
				<!-- 预留的协议内容结构 -->
				<view class="agreement-section" style="display: none;">
					<text class="section-title">第一条 协议的范围</text>
					<text class="section-content">
						本协议是您与本平台之间关于您使用本平台服务所订立的协议。
					</text>
				</view>
				
				<view class="agreement-section" style="display: none;">
					<text class="section-title">第二条 服务内容</text>
					<text class="section-content">
						本平台为用户提供相关的服务内容...
					</text>
				</view>
				
				<view class="agreement-section" style="display: none;">
					<text class="section-title">第三条 用户权利与义务</text>
					<text class="section-content">
						用户在使用本平台服务时享有相应权利并承担相应义务...
					</text>
				</view>
				
				<view class="agreement-section" style="display: none;">
					<text class="section-title">第四条 免责声明</text>
					<text class="section-content">
						在法律允许的范围内，本平台对以下情况不承担责任...
					</text>
				</view>
				
				<view class="agreement-section" style="display: none;">
					<text class="section-title">第五条 协议的修改</text>
					<text class="section-content">
						本平台有权根据需要修改本协议条款...
					</text>
				</view>
			</view>
			
			<!-- 底部提示 -->
			<view class="agreement-footer">
				<text class="footer-text">
					如您对本协议有任何疑问，请联系客服。
				</text>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();

export default {
	data() {
		return {
			theme: app.globalData.theme, // 全局颜色变量
			CustomBar: this.CustomBar, // 自定义导航栏高度
			StatusBar: this.StatusBar // 状态栏高度
		};
	},
	
	onLoad() {
		// 页面加载时的逻辑
	},
	
	methods: {
		// 预留方法：联系客服
		contactService() {
			// 可以跳转到客服页面或显示客服联系方式
			uni.showModal({
				title: '联系客服',
				content: '客服功能待开发，请稍后再试',
				showCancel: false
			});
		}
	}
};
</script>

<style scoped>
.agreement-container {
	min-height: 100vh;
	background-color: #fff;
}

.agreement-content {
	padding: 40rpx;
}

.agreement-header {
	text-align: center;
	margin-bottom: 60rpx;
	padding-bottom: 40rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.agreement-title {
	display: block;
	font-size: 44rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.update-time {
	display: block;
	font-size: 24rpx;
	color: #999;
}

.agreement-body {
	line-height: 1.8;
	color: #333;
}

.content-placeholder {
	text-align: center;
	padding: 120rpx 40rpx;
	background-color: #f8f8f8;
	border-radius: 20rpx;
	margin-bottom: 40rpx;
}

.placeholder-text {
	display: block;
	font-size: 32rpx;
	color: #666;
	margin-bottom: 30rpx;
	font-weight: bold;
}

.placeholder-desc {
	display: block;
	font-size: 26rpx;
	color: #999;
	line-height: 1.6;
}

.agreement-section {
	margin-bottom: 40rpx;
}

.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.section-content {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	text-indent: 2em;
}

.agreement-footer {
	margin-top: 80rpx;
	padding-top: 40rpx;
	border-top: 2rpx solid #f0f0f0;
	text-align: center;
}

.footer-text {
	font-size: 26rpx;
	color: #999;
}
</style>
