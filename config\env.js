/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
export default {
  /**
   * 服务器地址，即后台访问地址；本地开发填http://localhost:8082即可，如果要用真机调试要把localhost换成局域网ip，手机和电脑要处于同一局域网中；
   * 供app端、小程序端使用；注意！h5端比较特殊本地环境在joolun-plus-uniapp/manifest.json中配置转发，开发环境在nginx中配置转发，部署文档中有详细说明
   */
  // basePath: 'https://show.gocreateone.com',
  basePath: 'http://drone.gocreateone.com',

  /**
   * 点金计划小片链接
   */
  receiptPath: 'https://show.gocreateone.com',
  // basePath: 'https://localhost:9999',
  /**
   * 租户ID；
   * 仅供APP端使用
   */
  tenantId: '1',
  /**
   * 分享链接、海报二维码链接域名
   * 供app端和h5端生成分享链接、海报二维码链接时使用【最后不需要加斜杠】
   */
  h5HostUrl: 'http://show.gocreateone.com',
  /**
   * 公众号appId；
   * 供app端和h5端生成分享链接、海报二维码链接时使用
   */
  wxAppId: 'wxcb76d8ecc0bd1576',
  /**
   * 版本更新地址，取的是后台（joolun-plus-ui/public）下的一个json文件，App启动时会自动请求该文件然后判断是否需要更新，json格式请查看 /public/APPUpdate/APPUpdate.md；
   * 仅供APP端使用
   */
  appUpdateUrl: 'https://show.gocreateone.com/AppVersionMall.json',
  /**
   * 是否显示 隐私政策、用户协议 相关功能。目前所有app上架到应用宝，苹果等各个商店平台需要隐私政策信息。因为上架手续繁琐，如有需要请查看文档进行修改。
   * 仅供APP端使用
   */
  showPrivacyPolicy: true,
  /**
   * 隐私政策网络地址
   * 仅供APP端使用
   */
  privacyPolicyUrl:'https://www.gocreateone.com/h-nd-99.html#_jcp=4_1',
  /**
   * 用户协议网络地址
   * 仅供APP端使用
   */
  protocolUrl:'https://www.gocreateone.com/h-nd-100.html#_jcp=4_1',
};
